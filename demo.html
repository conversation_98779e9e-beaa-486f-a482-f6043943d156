<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OCR插件演示</title>
    <link rel="stylesheet" href="assets/style.css">
    <style>
        .demo-controls {
            position: fixed;
            top: 10px;
            right: 10px;
            background: var(--bg-primary);
            padding: 12px;
            border-radius: 8px;
            box-shadow: 0 4px 20px var(--shadow-medium);
            z-index: 1000;
            border: 1px solid var(--border-primary);
            min-width: 200px;
        }
        .demo-title {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--text-primary);
        }
        .demo-btn {
            display: block;
            width: 100%;
            margin: 4px 0;
            padding: 6px 10px;
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            background: var(--bg-secondary);
            color: var(--text-primary);
            cursor: pointer;
            font-size: 12px;
            transition: all 0.2s ease;
        }
        .demo-btn:hover {
            background: var(--bg-tertiary);
            transform: translateY(-1px);
        }
        .demo-section {
            margin-bottom: 12px;
            padding-bottom: 8px;
            border-bottom: 1px solid var(--border-primary);
        }
        .demo-section:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
    </style>
</head>
<body>
    <div class="demo-controls">
        <div class="demo-section">
            <div class="demo-title">界面测试</div>
            <button class="demo-btn" onclick="testImagePreview()">测试图片预览</button>
            <button class="demo-btn" onclick="testResult()">测试识别结果</button>
            <button class="demo-btn" onclick="testLoading()">测试加载状态</button>
        </div>
        
        <div class="demo-section">
            <div class="demo-title">状态测试</div>
            <button class="demo-btn" onclick="setStatus('ready')">就绪状态</button>
            <button class="demo-btn" onclick="setStatus('processing')">识别中</button>
            <button class="demo-btn" onclick="setStatus('error')">错误状态</button>
        </div>
        
        <div class="demo-section">
            <div class="demo-title">服务测试</div>
            <button class="demo-btn" onclick="setService('baidu')">百度OCR</button>
            <button class="demo-btn" onclick="setService('tencent')">腾讯云OCR</button>
            <button class="demo-btn" onclick="setService('llm')">LLM视觉</button>
        </div>
        
        <div class="demo-section">
            <div class="demo-title">其他</div>
            <button class="demo-btn" onclick="showConfig()">显示配置</button>
            <button class="demo-btn" onclick="showMain()">显示主界面</button>
            <button class="demo-btn" onclick="clearAll()">清空所有</button>
        </div>
    </div>

    <div id="app">
        <!-- 主界面 -->
        <div id="main-view" class="view">
            <div class="main-container">
                <!-- 左侧操作面板 -->
                <div class="left-panel">
                    <div class="panel-header">
                        <h1 class="app-title">OCR识别</h1>
                        <div class="header-controls">
                            <button id="theme-toggle-main" class="header-btn" title="切换主题">🌙</button>
                            <button id="config-btn" class="config-btn">⚙️</button>
                        </div>
                    </div>
                    
                    <!-- 图片预览区域 -->
                    <div class="image-preview-area">
                        <div id="image-preview" class="image-preview" style="display: none;">
                            <img id="preview-img" class="preview-img" alt="预览图片">
                        </div>
                        <div id="preview-placeholder" class="preview-placeholder">
                            <div class="placeholder-icon">🖼️</div>
                            <div class="placeholder-text">图片预览区域</div>
                            <div class="placeholder-hint">选择图片或截图后将在此显示</div>
                        </div>
                    </div>
                    
                    <!-- 底部控制区域 -->
                    <div class="bottom-controls">
                        <div class="action-buttons">
                            <button id="screenshot-btn" class="action-btn primary">
                                <span class="btn-icon">📷</span>
                                <span class="btn-text">截图</span>
                            </button>
                            <button id="upload-btn" class="action-btn">
                                <span class="btn-icon">📁</span>
                                <span class="btn-text">选择</span>
                            </button>
                        </div>
                        
                        <div class="status-info">
                            <div class="status-item">
                                <span class="status-label">服务:</span>
                                <span id="current-service" class="status-value">百度OCR</span>
                            </div>
                            <div class="status-item">
                                <span class="status-label">状态:</span>
                                <span id="recognition-status" class="status-value ready">就绪</span>
                            </div>
                        </div>
                    </div>
                    
                    <div id="loading" class="loading-panel" style="display: none;">
                        <div class="loading-content">
                            <div class="spinner"></div>
                            <div class="loading-text">
                                <div class="loading-title">正在识别中...</div>
                                <div class="loading-desc">请稍候，正在处理您的图片</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 右侧结果面板 -->
                <div class="right-panel">
                    <div class="result-header">
                        <h3 class="result-title">识别结果</h3>
                    </div>
                    
                    <div class="result-content">
                        <textarea id="result-text" class="result-text" placeholder="识别结果将显示在这里，您可以直接编辑...&#10;&#10;💡 提示：&#10;• 点击左侧按钮开始识别&#10;• 支持截图和本地图片识别&#10;• 识别结果可直接编辑&#10;• 支持亮色/暗色主题切换"></textarea>
                    </div>
                    
                    <div class="result-controls">
                        <label class="checkbox-label">
                            <input type="checkbox" id="remove-linebreaks">
                            <span class="checkbox-text">去除换行符</span>
                        </label>
                        <div class="control-buttons">
                            <button id="copy-btn" class="control-btn primary">
                                <span class="btn-icon">📋</span>
                                <span class="btn-text">复制</span>
                            </button>
                            <button id="clear-btn" class="control-btn">
                                <span class="btn-icon">🗑️</span>
                                <span class="btn-text">清空</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 配置界面 -->
        <div id="config-view" class="view" style="display: none;">
            <div class="config-header">
                <button id="back-btn" class="back-btn">←</button>
                <h1 class="config-title">OCR配置</h1>
                <div class="theme-toggle">
                    <button id="theme-toggle-btn" class="theme-btn" title="切换主题">🌙</button>
                </div>
            </div>
            
            <div class="config-content">
                <div class="config-section">
                    <h3>OCR服务选择</h3>
                    <select id="ocr-service" class="select-input">
                        <option value="baidu">百度OCR</option>
                        <option value="tencent">腾讯云OCR</option>
                        <option value="aliyun">阿里云OCR</option>
                        <option value="llm">LLM视觉模型</option>
                    </select>
                </div>
                
                <div class="config-section">
                    <h4>百度OCR配置</h4>
                    <div class="form-group">
                        <label>API Key:</label>
                        <div class="input-with-toggle">
                            <input type="password" id="baidu-api-key" placeholder="请输入百度OCR API Key">
                            <button type="button" class="toggle-password" title="显示/隐藏API Key">
                                <span class="eye-icon">👁️</span>
                            </button>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>Secret Key:</label>
                        <div class="input-with-toggle">
                            <input type="password" id="baidu-secret-key" placeholder="请输入百度OCR Secret Key">
                            <button type="button" class="toggle-password" title="显示/隐藏Secret Key">
                                <span class="eye-icon">👁️</span>
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="config-actions">
                    <button id="save-config-btn" class="btn primary">保存配置</button>
                    <button id="test-config-btn" class="btn">测试连接</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 主题切换功能
        function toggleTheme() {
            const currentTheme = document.documentElement.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            
            document.documentElement.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);
            updateThemeIcon(newTheme);
        }

        function updateThemeIcon(theme) {
            const themeBtns = document.querySelectorAll('#theme-toggle-main, #theme-toggle-btn');
            const icon = theme === 'dark' ? '☀️' : '🌙';
            const title = theme === 'dark' ? '切换到亮色主题' : '切换到暗色主题';
            
            themeBtns.forEach(btn => {
                if (btn) {
                    btn.textContent = icon;
                    btn.title = title;
                }
            });
        }

        function loadTheme() {
            const savedTheme = localStorage.getItem('theme') || 'light';
            document.documentElement.setAttribute('data-theme', savedTheme);
            updateThemeIcon(savedTheme);
        }

        // 测试函数
        function testImagePreview() {
            const canvas = document.createElement('canvas');
            canvas.width = 300;
            canvas.height = 200;
            const ctx = canvas.getContext('2d');
            
            ctx.fillStyle = 'var(--bg-secondary)';
            ctx.fillRect(0, 0, 300, 200);
            ctx.fillStyle = 'var(--text-primary)';
            ctx.font = '20px Arial';
            ctx.fillText('测试图片', 100, 100);
            ctx.fillText('Test Image', 100, 130);
            
            const testImage = canvas.toDataURL();
            
            const imagePreview = document.getElementById('image-preview');
            const previewImg = document.getElementById('preview-img');
            const placeholder = document.getElementById('preview-placeholder');
            
            previewImg.src = testImage;
            imagePreview.style.display = 'flex';
            placeholder.style.display = 'none';
        }

        function testResult() {
            const resultText = document.getElementById('result-text');
            resultText.value = '这是一个测试识别结果\n包含多行文字\n用于测试界面显示效果\n\n测试内容：\n• 中文识别测试\n• 英文识别 English Text Recognition\n• 数字识别 123456789\n• 符号识别 !@#$%^&*()\n• 混合内容测试 Mixed Content Test\n\n界面特性：\n✓ 支持亮色/暗色主题切换\n✓ 紧凑的左右布局设计\n✓ 图片预览功能\n✓ 实时状态显示';
        }

        function testLoading() {
            const loading = document.getElementById('loading');
            loading.style.display = 'block';
            setStatus('processing');
            
            setTimeout(() => {
                loading.style.display = 'none';
                setStatus('ready');
            }, 3000);
        }

        function setStatus(status) {
            const statusEl = document.getElementById('recognition-status');
            const statusTexts = {
                'ready': '就绪',
                'processing': '识别中',
                'error': '错误'
            };
            
            statusEl.textContent = statusTexts[status] || status;
            statusEl.className = `status-value ${status}`;
        }

        function setService(service) {
            const serviceEl = document.getElementById('current-service');
            const serviceNames = {
                'baidu': '百度OCR',
                'tencent': '腾讯云OCR',
                'aliyun': '阿里云OCR',
                'llm': 'LLM视觉'
            };
            
            serviceEl.textContent = serviceNames[service] || service;
        }

        function showConfig() {
            document.getElementById('main-view').style.display = 'none';
            document.getElementById('config-view').style.display = 'block';
        }

        function showMain() {
            document.getElementById('config-view').style.display = 'none';
            document.getElementById('main-view').style.display = 'block';
        }

        function clearAll() {
            const imagePreview = document.getElementById('image-preview');
            const placeholder = document.getElementById('preview-placeholder');
            imagePreview.style.display = 'none';
            placeholder.style.display = 'flex';
            
            document.getElementById('result-text').value = '';
            document.getElementById('loading').style.display = 'none';
            setStatus('ready');
        }

        // 事件监听
        document.getElementById('clear-btn').addEventListener('click', clearAll);
        document.getElementById('copy-btn').addEventListener('click', () => {
            const text = document.getElementById('result-text').value;
            if (text) {
                navigator.clipboard.writeText(text).then(() => {
                    alert('已复制到剪贴板');
                });
            }
        });
        
        document.getElementById('theme-toggle-main').addEventListener('click', toggleTheme);
        document.getElementById('theme-toggle-btn').addEventListener('click', toggleTheme);
        document.getElementById('config-btn').addEventListener('click', showConfig);
        document.getElementById('back-btn').addEventListener('click', showMain);
        
        // 初始化
        loadTheme();
    </script>
</body>
</html>
