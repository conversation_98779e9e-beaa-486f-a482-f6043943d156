* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    color: #333;
    overflow: hidden;
    margin: 0;
    padding: 0;
}

.view {
    height: 100vh;
    overflow: hidden;
}

/* 主容器布局 */
.main-container {
    display: flex;
    height: 100vh;
    background: #f8fafc;
}

/* 左侧面板 */
.left-panel {
    width: 280px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 16px;
    display: flex;
    flex-direction: column;
    box-shadow: 2px 0 20px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.app-title {
    color: white;
    font-size: 18px;
    font-weight: 600;
    margin: 0;
}

.config-btn, .back-btn {
    background: rgba(255, 255, 255, 0.15);
    border: none;
    color: white;
    padding: 8px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 16px;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.config-btn:hover, .back-btn:hover {
    background: rgba(255, 255, 255, 0.25);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 图片预览区域 */
.image-preview-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    margin-bottom: 16px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    overflow: hidden;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.image-preview {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.preview-img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.preview-placeholder {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 40px 20px;
    color: rgba(255, 255, 255, 0.8);
}

.placeholder-icon {
    font-size: 48px;
    margin-bottom: 12px;
    opacity: 0.6;
}

.placeholder-text {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 8px;
}

.placeholder-hint {
    font-size: 12px;
    opacity: 0.7;
    line-height: 1.4;
}

/* 底部控制区域 */
.bottom-controls {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 12px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.action-buttons {
    display: flex;
    gap: 8px;
    margin-bottom: 12px;
}

.action-btn {
    flex: 1;
    background: rgba(255, 255, 255, 0.9);
    border: none;
    border-radius: 8px;
    padding: 12px 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    text-align: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    font-size: 12px;
    font-weight: 500;
    color: #333;
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    background: rgba(255, 255, 255, 1);
}

.action-btn.primary {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
}

.action-btn.primary:hover {
    background: linear-gradient(135deg, #3d8bfe 0%, #00d4fe 100%);
}

.action-btn .btn-icon {
    font-size: 18px;
}

.action-btn .btn-text {
    font-size: 11px;
    font-weight: 500;
}

/* 状态信息 */
.status-info {
    display: flex;
    justify-content: space-between;
    gap: 8px;
}

.status-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.status-label {
    color: rgba(255, 255, 255, 0.7);
    font-size: 10px;
    font-weight: 500;
    margin-bottom: 2px;
}

.status-value {
    color: white;
    font-size: 11px;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 4px;
    background: rgba(255, 255, 255, 0.1);
    min-height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.status-value.ready {
    background: rgba(34, 197, 94, 0.3);
    color: #22c55e;
}

.status-value.processing {
    background: rgba(251, 191, 36, 0.3);
    color: #fbbf24;
}

.status-value.error {
    background: rgba(239, 68, 68, 0.3);
    color: #ef4444;
}

/* 加载面板 */
.loading-panel {
    position: absolute;
    top: 50px;
    left: 16px;
    right: 16px;
    bottom: 80px;
    background: rgba(102, 126, 234, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 100;
}

.loading-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    text-align: center;
}

.loading-title {
    color: white;
    font-size: 14px;
    font-weight: 600;
}

.loading-desc {
    color: rgba(255, 255, 255, 0.8);
    font-size: 12px;
}

/* 右侧面板 */
.right-panel {
    flex: 1;
    background: white;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    padding: 16px;
}

.result-header {
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid #e5e7eb;
}

.result-title {
    color: #1f2937;
    font-size: 16px;
    font-weight: 600;
    margin: 0;
}

.result-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    margin-bottom: 12px;
}

/* 底部控制区域 */
.result-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: #f9fafb;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
    gap: 12px;
}

.checkbox-label {
    display: flex;
    align-items: center;
    font-size: 12px;
    color: #6b7280;
    margin: 0;
    cursor: pointer;
    transition: all 0.2s ease;
}

.checkbox-label:hover {
    color: #374151;
}

.checkbox-label input[type="checkbox"] {
    margin-right: 6px;
    width: auto;
    accent-color: #4facfe;
}

.checkbox-text {
    font-weight: 500;
}

.control-buttons {
    display: flex;
    gap: 8px;
}

.control-btn {
    padding: 6px 12px;
    border: none;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #f3f4f6;
    color: #6b7280;
    border: 1px solid #d1d5db;
    display: flex;
    align-items: center;
    gap: 4px;
}

.control-btn:hover {
    background: #e5e7eb;
    border-color: #9ca3af;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.control-btn.primary {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    border-color: transparent;
}

.control-btn.primary:hover {
    background: linear-gradient(135deg, #3d8bfe 0%, #00d4fe 100%);
    box-shadow: 0 2px 12px rgba(79, 172, 254, 0.3);
}

.control-btn .btn-icon {
    font-size: 12px;
}

.control-btn .btn-text {
    font-size: 11px;
}

#re-recognize-btn {
    background: #4facfe;
    color: white;
}

#re-recognize-btn:hover {
    background: #3d8bfe;
}

.result-text {
    flex: 1;
    background: #f8fafc;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    padding: 20px;
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Courier New', monospace;
    font-size: 14px;
    line-height: 1.6;
    white-space: pre-wrap;
    word-wrap: break-word;
    resize: none;
    width: 100%;
    box-sizing: border-box;
    transition: all 0.3s ease;
    color: #334155;
    overflow-y: auto;
}

.result-text:focus {
    outline: none;
    border-color: #4facfe;
    box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
    background: white;
}

.result-text::placeholder {
    color: #94a3b8;
    font-style: normal;
}

/* 旧的loading样式保持兼容 */
.loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px;
    color: white;
}

.spinner {
    width: 32px;
    height: 32px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-top: 3px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 配置界面样式 */
#config-view {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    padding: 24px;
}

#config-view .header {
    display: flex;
    align-items: center;
    margin-bottom: 32px;
    padding-bottom: 20px;
    border-bottom: 2px solid rgba(255, 255, 255, 0.2);
}

#config-view .header h1 {
    color: #1e293b;
    font-size: 28px;
    font-weight: 700;
    margin: 0 0 0 16px;
}

.config-content {
    background: white;
    border-radius: 16px;
    padding: 32px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    max-width: 800px;
    margin: 0 auto;
}

.config-section {
    margin-bottom: 32px;
    padding-bottom: 24px;
    border-bottom: 2px solid #f1f5f9;
}

.config-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.config-section h3 {
    color: #1e293b;
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.config-section h3::before {
    content: '⚙️';
    font-size: 18px;
}

.config-section h4 {
    color: #475569;
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 20px;
    padding: 12px 16px;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-radius: 10px;
    border-left: 4px solid #4facfe;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #374151;
    font-weight: 600;
    font-size: 14px;
}

.select-input, input[type="text"], input[type="password"], input[type="number"], textarea {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e5e7eb;
    border-radius: 10px;
    font-size: 14px;
    transition: all 0.3s ease;
    font-family: inherit;
    background: #f9fafb;
}

.select-input:focus, input[type="text"]:focus, input[type="password"]:focus, input[type="number"]:focus, textarea:focus {
    outline: none;
    border-color: #4facfe;
    box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
    background: white;
}

textarea {
    resize: vertical;
    min-height: 80px;
    line-height: 1.6;
}

/* 模型选择相关样式 */
.model-selection {
    display: flex;
    align-items: center;
}

.model-info {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 15px;
    margin-top: 10px;
}

.model-info h5 {
    margin: 0 0 10px 0;
    color: #495057;
    font-size: 14px;
    font-weight: 600;
}

.model-info .model-detail {
    display: flex;
    justify-content: space-between;
    margin: 5px 0;
    font-size: 13px;
}

.model-info .model-detail .label {
    font-weight: 500;
    color: #6c757d;
}

.model-info .model-detail .value {
    color: #495057;
}

.form-hint {
    display: block;
    margin-top: 5px;
    font-size: 12px;
    color: #6c757d;
    line-height: 1.4;
}

.loading-models {
    color: #6c757d;
    font-style: italic;
}

.error-models {
    color: #dc3545;
    font-size: 12px;
}

/* 复选框样式 */
input[type="checkbox"] {
    width: auto;
    margin-right: 8px;
}

label input[type="checkbox"] {
    margin-right: 8px;
}

/* API Key输入框和切换按钮样式 */
.input-with-toggle {
    position: relative;
    display: flex;
    align-items: center;
}

.input-with-toggle input {
    flex: 1;
    padding-right: 45px; /* 为按钮留出空间 */
}

.toggle-password {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    transition: background-color 0.2s ease;
    font-size: 16px;
    line-height: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 32px;
    height: 32px;
}

.toggle-password:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

.toggle-password:active {
    background-color: rgba(0, 0, 0, 0.1);
}

.eye-icon {
    display: inline-block;
    transition: opacity 0.2s ease;
}

.toggle-password.hidden .eye-icon {
    opacity: 0.6;
}

.toggle-password.visible .eye-icon {
    opacity: 1;
}

/* 为不同状态提供不同的图标 */
.toggle-password.hidden .eye-icon::after {
    content: '';
    position: absolute;
    width: 20px;
    height: 2px;
    background: #666;
    transform: rotate(45deg);
    top: 50%;
    left: 50%;
    margin-left: -10px;
    margin-top: -1px;
}

.config-actions {
    display: flex;
    gap: 15px;
    margin-top: 25px;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #f8f9fa;
    color: #333;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn.primary {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
}

.btn.primary:hover {
    background: linear-gradient(135deg, #3d8bfe 0%, #00d4fe 100%);
}

/* 置信度显示 */
.confidence-info {
    font-size: 12px;
    padding: 4px 8px;
    border-radius: 4px;
    font-weight: 500;
}

.confidence-info.high {
    background: #d4edda;
    color: #155724;
}

.confidence-info.medium {
    background: #fff3cd;
    color: #856404;
}

.confidence-info.low {
    background: #f8d7da;
    color: #721c24;
}

/* 拖拽区域样式 */
.drop-zone-content {
    text-align: center;
}

.drop-zone-icon {
    font-size: 48px;
    margin-bottom: 20px;
}

.drop-zone-text {
    font-size: 18px;
    font-weight: 500;
}

/* 进度条样式 */
.progress-container {
    width: 100%;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    transition: width 0.3s ease;
    width: 0%;
}

.progress-text {
    text-align: center;
    font-size: 14px;
    color: #666;
}

/* 移动端布局 */
.mobile-layout .action-buttons {
    flex-direction: column;
}

.mobile-layout .config-actions {
    flex-direction: column;
}

.mobile-layout .result-actions {
    flex-direction: column;
    gap: 5px;
}

.mobile-layout .header h1 {
    font-size: 20px;
}

.mobile-layout .action-btn {
    padding: 15px;
    font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .main-container {
        flex-direction: column;
    }

    .left-panel {
        width: 100%;
        height: 200px;
        min-height: 200px;
        max-height: 200px;
        padding: 12px;
    }

    .image-preview-area {
        margin-bottom: 8px;
    }

    .placeholder-icon {
        font-size: 32px;
        margin-bottom: 8px;
    }

    .placeholder-text {
        font-size: 14px;
        margin-bottom: 4px;
    }

    .placeholder-hint {
        font-size: 11px;
    }

    .bottom-controls {
        padding: 8px;
    }

    .action-btn {
        padding: 8px 6px;
        font-size: 11px;
    }

    .action-btn .btn-icon {
        font-size: 16px;
    }

    .action-btn .btn-text {
        font-size: 10px;
    }

    .status-label {
        font-size: 9px;
    }

    .status-value {
        font-size: 10px;
        padding: 1px 4px;
    }

    .right-panel {
        padding: 12px;
        min-height: calc(100vh - 200px);
    }

    .result-controls {
        flex-direction: column;
        gap: 8px;
        align-items: stretch;
    }

    .control-buttons {
        justify-content: center;
    }

    .config-content {
        padding: 20px;
        margin: 0 16px;
    }
}

@media (max-width: 480px) {
    .left-panel {
        height: 180px;
        min-height: 180px;
        max-height: 180px;
        padding: 10px;
    }

    .app-title {
        font-size: 16px;
    }

    .config-btn {
        width: 32px;
        height: 32px;
        font-size: 14px;
    }

    .action-buttons {
        gap: 6px;
    }

    .action-btn {
        padding: 6px 4px;
    }

    .right-panel {
        padding: 10px;
        min-height: calc(100vh - 180px);
    }

    .result-title {
        font-size: 14px;
    }

    .control-btn {
        padding: 4px 8px;
        font-size: 11px;
    }

    .checkbox-label {
        font-size: 11px;
    }
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}
