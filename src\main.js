// OCR插件主逻辑
class OCRPlugin {
    constructor() {
        this.configManager = new ConfigManager();
        this.ocrServices = new OCRServices();
        this.uiManager = new UIManager();
        this.modelManager = new ModelManager();
        this.config = this.configManager.getConfig();
        this.lastImageBase64 = null; // 保存最后一次识别的图片
        this.init();
    }

    init() {
        console.log('OCR应用初始化开始');

        this.uiManager.init();
        this.bindEvents();
        this.loadConfigUI();
        this.updateUIStatus();
        
        // 监听uTools插件进入事件
        window.addEventListener('utools-plugin-enter', (event) => {
            const { code, type, payload } = event.detail;
            console.log('收到插件进入事件:', { code, type, payload });
            this.handlePluginEnter(code, type, payload);
        });

        // 根据启动参数决定初始行为
        const feature = this.getCurrentFeature();
        console.log('当前功能代码:', feature);
        
        if (feature === 'ocr-main') {
            // OCR Pro主界面，只显示主界面不执行其他操作
            // 已经在uiManager.init()中显示了主界面，无需额外操作
        } else if (feature === 'ocr-config') {
            this.uiManager.showConfigView();
        } else if (feature === 'ocr-image') {
            // 检查是否有payload数据
            const payload = window.ocrAPI?.getPayload();
            if (payload) {
                // 如果有图片数据，直接处理图片
                this.handleImageInput();
            } else {
                // 如果没有图片数据，显示主界面并自动触发文件选择
                this.uiManager.showMainView();
                // 使用requestAnimationFrame确保DOM渲染完成后再触发文件选择
                requestAnimationFrame(() => {
                    setTimeout(() => {
                        const fileInput = document.getElementById('file-input');
                        if (fileInput) {
                            fileInput.click();
                            console.log('自动触发文件选择对话框');
                        } else {
                            console.error('文件输入元素未找到');
                            this.uiManager.showError('界面初始化失败，请手动点击选择图片按钮');
                        }
                    }, 200);
                });
            }
        } else if (feature === 'ocr-clipboard') {
            // 如果是剪切板图片识别功能，直接处理剪切板图片
            requestAnimationFrame(() => {
                this.handleImageInput();
            });
        } else if (feature === 'ocr-screenshot') {
            // 如果是截图识别功能，直接触发截图
            // 使用requestAnimationFrame确保界面准备就绪后再截图
            requestAnimationFrame(() => {
                this.takeScreenshot();
            });
        } else {
            // 如果没有明确的功能代码，检查是否有payload
            requestAnimationFrame(() => {
                this.checkForPayload();
            });
        }
        
        console.log('OCR应用初始化完成');
    }

    getCurrentFeature() {
        // 尝试多种方式获取当前功能代码
        if (window.ocrAPI?.getCurrentFeature) {
            const feature = window.ocrAPI.getCurrentFeature();
            if (feature) return feature;
        }
        
        // 从URL参数获取
        const urlParams = new URLSearchParams(window.location.search);
        const featureFromUrl = urlParams.get('feature');
        if (featureFromUrl) return featureFromUrl;
        
        // 从全局变量获取
        if (window.currentFeatureCode) return window.currentFeatureCode;
        
        // 默认返回null
        return null;
    }

    bindEvents() {
        // 主界面事件
        document.getElementById('screenshot-btn').addEventListener('click', () => {
            this.takeScreenshot();
        });

        document.getElementById('upload-btn').addEventListener('click', () => {
            document.getElementById('file-input').click();
        });

        document.getElementById('file-input').addEventListener('change', (e) => {
            this.handleFileSelect(e);
        });

        document.getElementById('config-btn').addEventListener('click', () => {
            this.uiManager.showConfigView();
        });

        document.getElementById('copy-btn').addEventListener('click', () => {
            this.copyResult();
        });

        document.getElementById('clear-btn').addEventListener('click', () => {
            this.uiManager.clearResult();
        });

        // 配置界面事件
        document.getElementById('back-btn').addEventListener('click', () => {
            this.uiManager.showMainView();
        });

        document.getElementById('ocr-service').addEventListener('change', (e) => {
            this.uiManager.switchConfigSection(e.target.value);
        });

        // LLM平台切换事件
        document.getElementById('llm-platform').addEventListener('change', (e) => {
            this.handlePlatformChange(e.target.value);
        });

        // 刷新模型列表事件
        document.getElementById('refresh-models-btn').addEventListener('click', () => {
            this.refreshModelList();
        });

        // 自定义模型切换事件
        document.getElementById('use-custom-model').addEventListener('change', (e) => {
            this.toggleCustomModel(e.target.checked);
        });

        // 模型选择变化事件
        document.getElementById('llm-model-select').addEventListener('change', (e) => {
            this.handleModelChange(e.target.value);
        });

        // API Key显示/隐藏切换事件
        document.getElementById('toggle-api-key').addEventListener('click', () => {
            this.toggleApiKeyVisibility();
        });

        // API Key输入变化事件（保存到对应平台）
        document.getElementById('llm-api-key').addEventListener('input', (e) => {
            this.handleApiKeyChange(e.target.value);
        });

        document.getElementById('save-config-btn').addEventListener('click', () => {
            this.saveConfig();
        });

        document.getElementById('test-config-btn').addEventListener('click', () => {
            this.testConfig();
        });
    }

    // 截图功能
    takeScreenshot() {
        try {
            this.uiManager.showLoading('正在截图...<br><small>按ESC键可取消截图</small>');

            window.ocrAPI.screenCapture((imageBase64) => {
                try {
                    if (imageBase64) {
                        // 截图成功，进行OCR识别
                        this.performOCR(imageBase64);
                    } else {
                        // 截图失败或取消
                        this.uiManager.hideLoading();
                        this.uiManager.showError('截图已取消或失败');
                        console.log('截图操作被取消或失败');
                    }
                } catch (error) {
                    // 截图回调中的错误处理
                    this.uiManager.hideLoading();
                    this.uiManager.showError('截图处理失败: ' + error.message);
                }
            });
        } catch (error) {
            // 截图启动失败
            this.uiManager.hideLoading();
            this.uiManager.showError('无法启动截图功能: ' + error.message);
        }
    }

    // 处理文件选择
    handleFileSelect(event) {
        try {
            const file = event.target.files[0];
            if (!file) return;

            if (!file.type.startsWith('image/')) {
                this.uiManager.showError('请选择图片文件');
                // 重置文件输入框
                event.target.value = '';
                return;
            }

            // 确保插件窗口显示在前台
            try {
                if (window.ocrAPI && window.ocrAPI.showMainWindow) {
                    window.ocrAPI.showMainWindow();
                } else if (typeof utools !== 'undefined' && utools.showMainWindow) {
                    utools.showMainWindow();
                }
            } catch (e) {
                console.log('显示主窗口失败:', e);
            }

            this.uiManager.showLoading('正在读取图片...');

            const reader = new FileReader();
            reader.onload = (e) => {
                try {
                    this.performOCR(e.target.result);
                    // 重置文件输入框，允许选择同一个文件
                    event.target.value = '';
                } catch (error) {
                    this.uiManager.hideLoading();
                    this.uiManager.showError('图片读取失败: ' + error.message);
                    // 重置文件输入框
                    event.target.value = '';
                }
            };

            reader.onerror = () => {
                this.uiManager.hideLoading();
                this.uiManager.showError('图片读取失败，请重试');
                // 重置文件输入框
                event.target.value = '';
            };

            reader.readAsDataURL(file);
        } catch (error) {
            this.uiManager.hideLoading();
            this.uiManager.showError('文件处理失败: ' + error.message);
            // 重置文件输入框
            event.target.value = '';
        }
    }

    // 处理图片输入（来自uTools的图片匹配）
    handleImageInput(payload = null) {
        try {
            // 如果没有传入参数，从uTools获取传入的图片数据
            if (!payload) {
                payload = window.ocrAPI.getPayload();
            }

            console.log('处理图片输入，payload:', payload);

            // 确保窗口显示在前台
            try {
                if (window.ocrAPI && window.ocrAPI.showMainWindow) {
                    window.ocrAPI.showMainWindow();
                }
            } catch (e) {
                console.log('显示主窗口失败:', e);
            }

            if (payload) {
                // 处理直接的base64图片数据（来自剪切板或截图）
                if (typeof payload === 'string' && payload.startsWith('data:image/')) {
                    this.performOCR(payload);
                    return;
                }

                // 提取文件路径的统一方法
                const extractedFilePath = this.extractImageFilePath(payload);
                if (extractedFilePath) {
                    console.log('检测到图片文件路径，开始自动识别:', extractedFilePath);
                    // 直接调用自动识别，而不是显示选择界面
                    this.loadImageFromPath(extractedFilePath);
                    return;
                }

                // 处理对象格式的payload中的base64数据
                if (typeof payload === 'object' && payload.type) {
                    if (payload.type === 'img' && payload.data) {
                        // 图片类型
                        if (typeof payload.data === 'string' && payload.data.startsWith('data:image/')) {
                            this.performOCR(payload.data);
                            return;
                        }
                    }
                }

                // 如果payload存在但格式不识别，尝试更宽松的处理
                console.log('无法识别的payload格式:', payload, '类型:', typeof payload);

                // 尝试处理其他可能的base64格式
                if (typeof payload === 'object' && payload !== null) {
                    // 检查是否有base64数据
                    const possibleBase64 = [payload.base64, payload.data, payload.content];
                    for (const base64 of possibleBase64) {
                        if (typeof base64 === 'string' && base64.startsWith('data:image/')) {
                            console.log('找到可能的base64数据');
                            this.performOCR(base64);
                            return;
                        }
                    }
                }

                // 最后的错误提示
                this.uiManager.showError('不支持的图片格式或数据，请手动选择图片文件');
                this.uiManager.showMainView();
            } else {
                // 没有图片数据，显示选择图片的界面
                this.uiManager.showMainView();
                console.log('没有检测到图片数据，请手动选择图片文件');
            }
        } catch (error) {
            console.error('处理图片输入失败:', error);
            this.uiManager.showError('图片处理失败: ' + error.message);
        }
    }

    // 从payload中提取图片文件路径的统一方法
    extractImageFilePath(payload) {
        try {
            // 处理文件路径数组（来自文件拖拽或选择）
            if (Array.isArray(payload)) {
                const filePath = payload[0];
                if (typeof filePath === 'string' && filePath.match(/\.(png|jpg|jpeg|gif|bmp|webp)$/i)) {
                    return filePath;
                }
            }

            // 处理对象格式的payload
            if (typeof payload === 'object' && payload !== null) {
                // 处理files类型
                if (payload.type === 'files' && payload.data) {
                    const filePath = Array.isArray(payload.data) ? payload.data[0] : payload.data;
                    if (typeof filePath === 'string' && filePath.match(/\.(png|jpg|jpeg|gif|bmp|webp)$/i)) {
                        return filePath;
                    }
                }

                // 检查是否有文件路径相关的属性
                const possiblePaths = [payload.path, payload.file, payload.src, payload.url, payload.filePath];
                for (const path of possiblePaths) {
                    if (typeof path === 'string' && path.match(/\.(png|jpg|jpeg|gif|bmp|webp)$/i)) {
                        return path;
                    }
                }
            }

            // 如果是字符串，检查是否是文件路径
            if (typeof payload === 'string' && payload.match(/\.(png|jpg|jpeg|gif|bmp|webp)$/i)) {
                return payload;
            }

            return null;
        } catch (error) {
            console.error('提取文件路径失败:', error);
            return null;
        }
    }

    // 从文件路径加载图片
    async loadImageFromPath(filePath) {
        try {
            this.uiManager.showLoading('正在自动读取图片...');

            // 检查文件路径格式
            if (!filePath || typeof filePath !== 'string') {
                throw new Error('无效的文件路径');
            }

            // 检查文件扩展名
            if (!filePath.match(/\.(png|jpg|jpeg|gif|bmp|webp)$/i)) {
                throw new Error('不支持的图片格式');
            }

            console.log('开始自动读取图片文件:', filePath);

            // 尝试多种方式读取文件
            let fileContent = null;
            let lastError = null;

            // 方法1: 尝试使用uTools的文件读取API（如果可用）
            if (window.ocrAPI && window.ocrAPI.readFile) {
                try {
                    console.log('尝试使用uTools API读取文件...');
                    const fileData = window.ocrAPI.readFile(filePath);
                    if (fileData) {
                        // 将文件数据转换为base64
                        fileContent = await this.arrayBufferToBase64(fileData, filePath);
                    }
                } catch (utoolsError) {
                    console.log('uTools API读取失败:', utoolsError);
                    lastError = utoolsError;
                }
            }

            // 方法2: 尝试使用fetch读取
            if (!fileContent) {
                try {
                    console.log('尝试使用fetch读取文件...');
                    const fileUrl = this.normalizeFilePath(filePath);
                    const response = await fetch(fileUrl);
                    if (response.ok) {
                        const blob = await response.blob();
                        fileContent = await this.blobToBase64(blob);
                    } else {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                } catch (fetchError) {
                    console.log('fetch方式读取失败:', fetchError);
                    lastError = fetchError;
                }
            }

            // 方法3: 如果fetch失败，尝试使用XMLHttpRequest
            if (!fileContent) {
                try {
                    console.log('尝试使用XMLHttpRequest读取文件...');
                    fileContent = await this.loadFileWithXHR(filePath);
                } catch (xhrError) {
                    console.log('XHR方式读取失败:', xhrError);
                    lastError = xhrError;
                }
            }

            // 方法4: 尝试使用HTML5 File API（如果路径是本地文件）
            if (!fileContent && this.isLocalPath(filePath)) {
                try {
                    console.log('尝试使用File API读取文件...');
                    fileContent = await this.loadFileWithFileAPI();
                } catch (fileApiError) {
                    console.log('File API读取失败:', fileApiError);
                    lastError = fileApiError;
                }
            }

            // 如果所有方法都失败，回退到手动选择模式
            if (!fileContent) {
                this.uiManager.hideLoading();
                console.log('自动读取失败，回退到手动选择模式');

                // 显示友好的错误提示，并提供手动选择选项
                const errorMsg = `无法自动读取图片文件：${lastError?.message || '未知错误'}`;
                this.uiManager.showError(errorMsg + '\n\n请点击"选择图片"按钮手动选择文件');
                this.uiManager.showMainView();
                return;
            }

            console.log('图片文件读取成功，开始OCR识别');
            // 执行OCR
            this.performOCR(fileContent);

        } catch (error) {
            this.uiManager.hideLoading();
            console.error('图片加载过程中发生错误:', error);

            // 显示错误信息并回退到手动选择
            this.uiManager.showError('图片加载失败: ' + error.message + '\n\n请点击"选择图片"按钮手动选择文件');
            this.uiManager.showMainView();
        }
    }
    
    // 将Blob转换为Base64
    blobToBase64(blob) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = () => resolve(reader.result);
            reader.onerror = reject;
            reader.readAsDataURL(blob);
        });
    }

    // 将ArrayBuffer转换为Base64
    async arrayBufferToBase64(arrayBuffer, filePath) {
        try {
            // 根据文件扩展名确定MIME类型
            const extension = filePath.split('.').pop().toLowerCase();
            const mimeTypes = {
                'png': 'image/png',
                'jpg': 'image/jpeg',
                'jpeg': 'image/jpeg',
                'gif': 'image/gif',
                'bmp': 'image/bmp',
                'webp': 'image/webp'
            };
            const mimeType = mimeTypes[extension] || 'image/jpeg';

            // 将ArrayBuffer转换为Blob，然后转换为Base64
            const blob = new Blob([arrayBuffer], { type: mimeType });
            return await this.blobToBase64(blob);
        } catch (error) {
            throw new Error('ArrayBuffer转换失败: ' + error.message);
        }
    }

    // 标准化文件路径
    normalizeFilePath(filePath) {
        try {
            // 处理Windows路径
            let normalizedPath = filePath.replace(/\\/g, '/');

            // 如果不是以file://开头，添加file://协议
            if (!normalizedPath.startsWith('file://')) {
                // 处理绝对路径
                if (normalizedPath.match(/^[a-zA-Z]:/)) {
                    // Windows绝对路径 (C:/path/to/file)
                    normalizedPath = 'file:///' + normalizedPath;
                } else if (normalizedPath.startsWith('/')) {
                    // Unix绝对路径 (/path/to/file)
                    normalizedPath = 'file://' + normalizedPath;
                } else {
                    // 相对路径，添加当前目录
                    normalizedPath = 'file:///' + normalizedPath;
                }
            }

            return normalizedPath;
        } catch (error) {
            console.error('路径标准化失败:', error);
            return filePath;
        }
    }

    // 检查是否是本地文件路径
    isLocalPath(filePath) {
        return filePath && (
            filePath.startsWith('file://') ||
            filePath.match(/^[a-zA-Z]:/) ||  // Windows路径
            filePath.startsWith('/') ||      // Unix路径
            filePath.startsWith('./') ||     // 相对路径
            filePath.startsWith('../')       // 相对路径
        );
    }

    // 使用XMLHttpRequest读取文件
    loadFileWithXHR(filePath) {
        return new Promise((resolve, reject) => {
            const xhr = new XMLHttpRequest();
            const normalizedPath = this.normalizeFilePath(filePath);
            xhr.open('GET', normalizedPath, true);
            xhr.responseType = 'blob';

            xhr.onload = async () => {
                if (xhr.status === 200 || xhr.status === 0) { // 本地文件可能返回0
                    try {
                        const base64 = await this.blobToBase64(xhr.response);
                        resolve(base64);
                    } catch (error) {
                        reject(error);
                    }
                } else {
                    reject(new Error(`文件读取失败: HTTP ${xhr.status}`));
                }
            };

            xhr.onerror = () => reject(new Error('网络错误或文件不存在'));
            xhr.send();
        });
    }

    // 使用File API读取文件（实验性）
    async loadFileWithFileAPI() {
        try {
            // 这个方法主要用于处理一些特殊情况
            // 在某些环境中可能不可用
            if (typeof File !== 'undefined' && typeof FileReader !== 'undefined') {
                // 尝试创建一个File对象（这在大多数浏览器环境中不会成功）
                // 但在某些特殊的运行时环境中可能有效
                throw new Error('File API方法在当前环境中不可用');
            } else {
                throw new Error('File API不支持');
            }
        } catch (error) {
            throw new Error('File API读取失败: ' + error.message);
        }
    }



    // 执行OCR识别
    async performOCR(imageBase64) {
        try {
            // 保存图片以便重新识别
            this.lastImageBase64 = imageBase64;

            // 显示图片预览
            this.uiManager.showImagePreview(imageBase64);

            // 获取当前界面上的配置
            const currentConfig = this.getCurrentConfig();

            const validation = this.configManager.validateConfig(currentConfig);
            if (!validation.valid) {
                // 确保隐藏加载状态
                this.uiManager.hideLoading();
                this.uiManager.showError(validation.error || '请先配置OCR服务');
                return;
            }

            this.uiManager.showLoading('正在识别文字...');

            try {
                const serviceConfig = this.configManager.getServiceConfig(currentConfig, currentConfig.service);
                const result = await this.ocrServices.performOCR(
                    imageBase64,
                    currentConfig.service,
                    serviceConfig
                );

                // 确保隐藏加载状态
                this.uiManager.hideLoading();

                if (result.success) {
                    this.uiManager.showResult(result.text, result.confidence, result.details);
                    // 显示重新识别按钮
                    this.showReRecognizeButton();
                } else {
                    this.uiManager.showError(result.error || '识别失败');
                }
            } catch (ocrError) {
                // 确保隐藏加载状态
                this.uiManager.hideLoading();

                // 处理特定的错误类型
                let errorMessage = 'OCR识别失败';
                if (ocrError.message) {
                    if (ocrError.message.includes('429')) {
                        errorMessage = 'API请求过于频繁，请稍后再试';
                    } else if (ocrError.message.includes('401')) {
                        errorMessage = 'API Key无效或已过期，请检查配置';
                    } else if (ocrError.message.includes('403')) {
                        errorMessage = 'API访问被拒绝，请检查权限设置';
                    } else if (ocrError.message.includes('500')) {
                        errorMessage = '服务器内部错误，请稍后重试';
                    } else {
                        errorMessage = `识别失败: ${ocrError.message}`;
                    }
                }

                this.uiManager.showError(errorMessage);
            }
        } catch (error) {
            // 最外层错误捕获，确保任何情况下都能隐藏加载状态
            this.uiManager.hideLoading();
            this.uiManager.showError('发生未知错误: ' + error.message);
        }
    }

    // 复制结果
    copyResult() {
        const resultText = document.getElementById('result-text').value;
        if (resultText) {
            window.ocrAPI.copyText(resultText);
            this.uiManager.showNotification('已复制到剪贴板');
        }
    }

    // 重新识别
    async reRecognize() {
        if (!this.lastImageBase64) {
            this.uiManager.showError('没有可重新识别的图片');
            return;
        }

        await this.performOCR(this.lastImageBase64);
    }

    // 显示重新识别按钮
    showReRecognizeButton() {
        const controlButtons = document.querySelector('.control-buttons');
        if (!controlButtons) return;

        // 检查是否已存在重新识别按钮
        let reRecognizeBtn = document.getElementById('re-recognize-btn');
        if (!reRecognizeBtn) {
            reRecognizeBtn = document.createElement('button');
            reRecognizeBtn.id = 're-recognize-btn';
            reRecognizeBtn.className = 'control-btn';
            reRecognizeBtn.innerHTML = '<span class="btn-icon">🔄</span><span class="btn-text">重识别</span>';
            reRecognizeBtn.title = '使用当前配置重新识别图片';

            // 添加事件监听器
            reRecognizeBtn.addEventListener('click', () => {
                this.reRecognize();
            });

            // 插入到复制按钮之前
            const copyBtn = document.getElementById('copy-btn');
            controlButtons.insertBefore(reRecognizeBtn, copyBtn);
        }
    }

    // 检查是否有payload数据
    checkForPayload() {
        const feature = this.getCurrentFeature();
        console.log('检查payload，当前功能:', feature);
        
        if (feature === 'ocr-image') {
            // 如果是图片识别功能，检查是否有图片数据
            const payloadData = window.ocrAPI?.getPayload();
            console.log('图片识别payload:', payloadData);
            
            if (payloadData) {
                // 检查payload是否包含图片数据
                const hasImageData = 
                    (payloadData.type === 'img' || payloadData.type === 'files') || // 对象格式
                    Array.isArray(payloadData) || // 文件路径数组
                    (typeof payloadData === 'string' && payloadData.startsWith('data:image/')); // base64图片
                
                if (hasImageData) {
                    this.handleImageInput(payloadData);
                } else {
                    // 如果payload格式不识别，显示主界面
                    this.uiManager.showMainView();
                    console.log('无法识别的payload格式，请手动选择图片');
                }
            } else {
                // 如果没有图片数据，显示主界面
                this.uiManager.showMainView();
                console.log('请点击选择文件按钮来选择图片');
            }
        } else if (feature === 'ocr-clipboard') {
            // 如果是剪切板识别功能，直接处理剪切板
            requestAnimationFrame(() => {
                this.handleImageInput();
            });
        } else if (feature === 'ocr-screenshot') {
            // 如果是截图识别功能，直接触发截图
            // 使用requestAnimationFrame确保界面准备就绪后再截图
            requestAnimationFrame(() => {
                this.takeScreenshot();
            });
        } else {
            // 如果没有明确的功能代码，检查是否有payload
            const payloadData = window.ocrAPI?.getPayload();
            if (payloadData) {
                console.log('检测到payload数据:', payloadData);
                this.handleImageInput(payloadData);
            } else {
                // 默认显示主界面
                this.uiManager.showMainView();
            }
        }
    }

    // 处理插件进入事件
    handlePluginEnter(code, type, payload) {
        console.log('处理插件进入:', { code, type, payload });
        
        // 根据功能代码执行相应操作
        if (code === 'ocr-main') {
            // OCR Pro主界面，只显示主界面不执行其他操作
            this.uiManager.showMainView();
        } else if (code === 'ocr-config') {
            this.uiManager.showConfigView();
        } else if (code === 'ocr-image') {
            // 检查是否有payload数据
            if (payload) {
                // 检查payload是否包含图片数据
                const hasImageData =
                    (payload.type === 'img' || payload.type === 'files') || // 对象格式
                    Array.isArray(payload) || // 文件路径数组
                    (typeof payload === 'string' && payload.startsWith('data:image/')); // base64图片

                if (hasImageData) {
                    // 如果有图片数据，直接处理图片
                    this.handleImageInput();
                } else {
                    // 如果payload格式不识别，显示主界面并自动触发文件选择
                    this.uiManager.showMainView();
                    requestAnimationFrame(() => {
                        setTimeout(() => {
                            const fileInput = document.getElementById('file-input');
                            if (fileInput) {
                                fileInput.click();
                                console.log('自动触发文件选择对话框（payload格式不识别）');
                            }
                        }, 200);
                    });
                }
            } else {
                // 如果没有图片数据，显示主界面并自动触发文件选择
                this.uiManager.showMainView();
                requestAnimationFrame(() => {
                    setTimeout(() => {
                        const fileInput = document.getElementById('file-input');
                        if (fileInput) {
                            fileInput.click();
                            console.log('自动触发文件选择对话框（无payload数据）');
                        }
                    }, 200);
                });
            }
        } else if (code === 'ocr-clipboard') {
            // 剪切板图片识别
            this.handleImageInput();
        } else if (code === 'ocr-screenshot') {
            // 使用requestAnimationFrame确保界面准备就绪后再截图
            requestAnimationFrame(() => {
                this.takeScreenshot();
            });
        }
    }

    // 加载配置到UI
    loadConfigUI() {
        // 设置服务选择
        document.getElementById('ocr-service').value = this.config.service;
        this.uiManager.switchConfigSection(this.config.service);

        // 百度配置
        document.getElementById('baidu-api-key').value = this.config.baidu?.apiKey || '';
        document.getElementById('baidu-secret-key').value = this.config.baidu?.secretKey || '';
        document.getElementById('baidu-type').value = this.config.baidu?.type || 'general_basic';

        // 腾讯云配置
        document.getElementById('tencent-secret-id').value = this.config.tencent?.secretId || '';
        document.getElementById('tencent-secret-key').value = this.config.tencent?.secretKey || '';

        // 阿里云配置
        document.getElementById('aliyun-access-key').value = this.config.aliyun?.accessKey || '';
        document.getElementById('aliyun-access-secret').value = this.config.aliyun?.accessSecret || '';

        // LLM配置
        const currentPlatform = this.config.llm?.platform || 'openai';
        document.getElementById('llm-platform').value = currentPlatform;

        // 根据当前平台设置API Key
        const currentApiKey = this.config.llm?.apiKeys?.[currentPlatform] || this.config.llm?.apiKey || '';
        document.getElementById('llm-api-key').value = currentApiKey;

        document.getElementById('llm-base-url').value = this.config.llm?.baseUrl || '';
        document.getElementById('llm-max-tokens').value = this.config.llm?.maxTokens || 1000;
        document.getElementById('llm-prompt').value = this.config.llm?.prompt || '请识别图片中的所有文字内容，直接输出文字，不要添加任何解释。';
        document.getElementById('use-custom-model').checked = this.config.llm?.useCustomModel || false;
        document.getElementById('custom-model-name').value = this.config.llm?.customModel || '';

        // 初始化API Key显示状态
        this.initApiKeyToggle();

        // 初始化平台相关配置
        this.handlePlatformChange(currentPlatform);
        this.toggleCustomModel(this.config.llm?.useCustomModel || false);
    }

    // 保存配置
    saveConfig() {
        const newConfig = {
            service: document.getElementById('ocr-service').value,
            baidu: {
                apiKey: document.getElementById('baidu-api-key').value,
                secretKey: document.getElementById('baidu-secret-key').value,
                type: document.getElementById('baidu-type').value
            },
            tencent: {
                secretId: document.getElementById('tencent-secret-id').value,
                secretKey: document.getElementById('tencent-secret-key').value
            },
            aliyun: {
                accessKey: document.getElementById('aliyun-access-key').value,
                accessSecret: document.getElementById('aliyun-access-secret').value
            },
            llm: {
                platform: document.getElementById('llm-platform').value,
                model: document.getElementById('llm-model-select').value,
                useCustomModel: document.getElementById('use-custom-model').checked,
                customModel: document.getElementById('custom-model-name').value,
                // 保存所有平台的API Key
                apiKeys: this.getCurrentApiKeys(),
                baseUrl: document.getElementById('llm-base-url').value,
                maxTokens: parseInt(document.getElementById('llm-max-tokens').value) || 1000,
                prompt: document.getElementById('llm-prompt').value
            }
        };

        const result = this.configManager.saveConfig(newConfig);
        if (result.success) {
            this.config = result.config;
            this.uiManager.showNotification('配置保存成功');
            // 更新UI状态显示
            this.updateUIStatus();
        } else {
            this.uiManager.showError('配置保存失败: ' + result.error);
        }
    }

    // 测试配置
    async testConfig() {
        try {
            // 获取当前界面上的配置，而不是已保存的配置
            const currentConfig = this.getCurrentConfig();

            const validation = this.configManager.validateConfig(currentConfig);
            if (!validation.valid) {
                this.uiManager.showError(validation.error || '请先完善配置信息');
                return;
            }

            this.uiManager.showNotification('正在测试连接...', 'info');

            // 创建一个简单的测试图片（白底黑字"TEST"）
            const canvas = document.createElement('canvas');
            canvas.width = 200;
            canvas.height = 100;
            const ctx = canvas.getContext('2d');
            ctx.fillStyle = 'white';
            ctx.fillRect(0, 0, 200, 100);
            ctx.fillStyle = 'black';
            ctx.font = '30px Arial';
            ctx.fillText('TEST', 70, 60);

            const testImage = canvas.toDataURL();

            try {
                const serviceConfig = this.configManager.getServiceConfig(currentConfig, currentConfig.service);
                const result = await this.ocrServices.performOCR(
                    testImage,
                    currentConfig.service,
                    serviceConfig
                );

                if (result.success) {
                    this.uiManager.showNotification('连接测试成功');
                } else {
                    // 处理特定的测试错误
                    let errorMessage = result.error || '连接测试失败';
                    if (errorMessage.includes('429')) {
                        errorMessage = 'API请求过于频繁，请稍后再试';
                    } else if (errorMessage.includes('401')) {
                        errorMessage = 'API Key无效或已过期';
                    } else if (errorMessage.includes('403')) {
                        errorMessage = 'API访问被拒绝，请检查权限';
                    }
                    this.uiManager.showError('连接测试失败: ' + errorMessage);
                }
            } catch (error) {
                let errorMessage = error.message || '连接测试失败';
                if (errorMessage.includes('429')) {
                    errorMessage = 'API请求过于频繁，请稍后再试';
                } else if (errorMessage.includes('401')) {
                    errorMessage = 'API Key无效或已过期';
                } else if (errorMessage.includes('403')) {
                    errorMessage = 'API访问被拒绝，请检查权限';
                } else if (errorMessage.includes('500')) {
                    errorMessage = '服务器内部错误，请稍后重试';
                }
                this.uiManager.showError('连接测试失败: ' + errorMessage);
            }
        } catch (error) {
            this.uiManager.showError('测试配置时发生错误: ' + error.message);
        }
    }

    // 获取当前界面上的配置
    getCurrentConfig() {
        return {
            service: document.getElementById('ocr-service').value,
            baidu: {
                apiKey: document.getElementById('baidu-api-key').value,
                secretKey: document.getElementById('baidu-secret-key').value,
                type: document.getElementById('baidu-type').value
            },
            tencent: {
                secretId: document.getElementById('tencent-secret-id').value,
                secretKey: document.getElementById('tencent-secret-key').value
            },
            aliyun: {
                accessKey: document.getElementById('aliyun-access-key').value,
                accessSecret: document.getElementById('aliyun-access-secret').value
            },
            llm: {
                platform: document.getElementById('llm-platform').value,
                model: document.getElementById('llm-model-select').value,
                useCustomModel: document.getElementById('use-custom-model').checked,
                customModel: document.getElementById('custom-model-name').value,
                // 保存所有平台的API Key
                apiKeys: this.getCurrentApiKeys(),
                baseUrl: document.getElementById('llm-base-url').value,
                maxTokens: parseInt(document.getElementById('llm-max-tokens').value) || 1000,
                prompt: document.getElementById('llm-prompt').value
            }
        };
    }

    // 处理平台切换
    async handlePlatformChange(platform) {
        const modelSelect = document.getElementById('llm-model-select');
        const modelInfo = document.getElementById('model-info');
        const apiKeyInput = document.getElementById('llm-api-key');

        // 清空当前选项
        modelSelect.innerHTML = '<option value="">加载中...</option>';
        modelInfo.style.display = 'none';

        // 切换到对应平台的API Key
        this.switchPlatformApiKey(platform);

        try {
            const apiKey = apiKeyInput.value;
            const baseUrl = document.getElementById('llm-base-url').value;

            if (apiKey) {
                // 尝试获取模型列表
                const models = await this.modelManager.getModels(platform, apiKey, baseUrl);
                this.populateModelSelect(models);
            } else {
                // 使用默认模型列表
                const platformInfo = this.modelManager.getPlatformInfo(platform);
                if (platformInfo) {
                    const defaultModels = platformInfo.defaultModels.map(model => ({
                        id: model,
                        name: model,
                        description: this.modelManager.getModelDescription(model),
                        isDefault: true
                    }));
                    this.populateModelSelect(defaultModels);
                }
            }

            // 设置当前选中的模型
            if (this.config.llm?.platform === platform && this.config.llm?.model) {
                modelSelect.value = this.config.llm.model;
                this.handleModelChange(this.config.llm.model);
            }
        } catch (error) {
            console.error('获取模型列表失败:', error);
            modelSelect.innerHTML = '<option value="">获取模型列表失败</option>';
        }
    }

    // 填充模型选择下拉框
    populateModelSelect(models) {
        const modelSelect = document.getElementById('llm-model-select');
        modelSelect.innerHTML = '';

        if (models.length === 0) {
            modelSelect.innerHTML = '<option value="">暂无可用模型</option>';
            return;
        }

        models.forEach(model => {
            const option = document.createElement('option');
            option.value = model.id;
            option.textContent = `${model.name}${model.isDefault ? ' (默认)' : ''}`;
            option.title = model.description || '';
            modelSelect.appendChild(option);
        });
    }

    // 刷新模型列表
    async refreshModelList() {
        const platform = document.getElementById('llm-platform').value;
        const apiKey = document.getElementById('llm-api-key').value;

        if (!apiKey) {
            this.uiManager.showError('请先输入API Key');
            return;
        }

        const refreshBtn = document.getElementById('refresh-models-btn');
        refreshBtn.disabled = true;
        refreshBtn.textContent = '刷新中...';

        try {
            // 清除缓存
            this.modelManager.clearCache();

            // 重新获取模型列表
            await this.handlePlatformChange(platform);

            this.uiManager.showNotification('模型列表已刷新');
        } catch (error) {
            this.uiManager.showError('刷新失败: ' + error.message);
        } finally {
            refreshBtn.disabled = false;
            refreshBtn.textContent = '刷新';
        }
    }

    // 切换自定义模型
    toggleCustomModel(useCustom) {
        const customGroup = document.getElementById('custom-model-group');
        const modelSelect = document.getElementById('llm-model-select');

        if (useCustom) {
            customGroup.style.display = 'block';
            modelSelect.disabled = true;
        } else {
            customGroup.style.display = 'none';
            modelSelect.disabled = false;
        }
    }

    // 处理模型变化
    handleModelChange(modelId) {
        if (!modelId) return;

        const platform = document.getElementById('llm-platform').value;
        const modelInfo = this.modelManager.getModelInfo(platform, modelId);

        if (modelInfo) {
            this.displayModelInfo(modelInfo);
        }
    }

    // 显示模型信息
    displayModelInfo(modelInfo) {
        const modelInfoDiv = document.getElementById('model-info');
        const modelDetails = document.getElementById('model-details');

        modelDetails.innerHTML = `
            <div class="model-detail">
                <span class="label">平台:</span>
                <span class="value">${modelInfo.platform}</span>
            </div>
            <div class="model-detail">
                <span class="label">模型ID:</span>
                <span class="value">${modelInfo.modelId}</span>
            </div>
            <div class="model-detail">
                <span class="label">描述:</span>
                <span class="value">${modelInfo.description}</span>
            </div>
            <div class="model-detail">
                <span class="label">API端点:</span>
                <span class="value">${modelInfo.apiEndpoint}</span>
            </div>
            <div class="model-detail">
                <span class="label">最大Token:</span>
                <span class="value">${modelInfo.maxTokens}</span>
            </div>
        `;

        modelInfoDiv.style.display = 'block';

        // 自动设置推荐的最大Token数
        const maxTokensInput = document.getElementById('llm-max-tokens');
        if (parseInt(maxTokensInput.value) === 1000) { // 只在默认值时自动设置
            maxTokensInput.value = modelInfo.maxTokens;
        }
    }

    // 初始化API Key显示切换功能
    initApiKeyToggle() {
        const toggleBtn = document.getElementById('toggle-api-key');

        // 设置初始状态
        toggleBtn.classList.add('hidden');
        toggleBtn.title = '显示API Key';
    }

    // 切换API Key显示状态
    toggleApiKeyVisibility() {
        const toggleBtn = document.getElementById('toggle-api-key');
        const apiKeyInput = document.getElementById('llm-api-key');

        if (apiKeyInput.type === 'password') {
            // 显示明文
            apiKeyInput.type = 'text';
            toggleBtn.classList.remove('hidden');
            toggleBtn.classList.add('visible');
            toggleBtn.title = '隐藏API Key';
        } else {
            // 隐藏为密码
            apiKeyInput.type = 'password';
            toggleBtn.classList.remove('visible');
            toggleBtn.classList.add('hidden');
            toggleBtn.title = '显示API Key';
        }
    }

    // 切换平台时更新API Key
    switchPlatformApiKey(platform) {
        const apiKeyInput = document.getElementById('llm-api-key');

        // 获取当前平台的API Key
        const currentApiKey = this.config.llm?.apiKeys?.[platform] || '';
        apiKeyInput.value = currentApiKey;

        // 更新placeholder提示
        const platformNames = {
            openai: 'OpenAI',
            anthropic: 'Anthropic',
            google: 'Google',
            custom: '自定义平台'
        };

        apiKeyInput.placeholder = `请输入${platformNames[platform] || platform} API Key`;
    }

    // 处理API Key输入变化
    handleApiKeyChange(apiKey) {
        const currentPlatform = document.getElementById('llm-platform').value;

        // 确保config.llm.apiKeys存在
        if (!this.config.llm) {
            this.config.llm = {};
        }
        if (!this.config.llm.apiKeys) {
            this.config.llm.apiKeys = {
                openai: '',
                anthropic: '',
                google: '',
                custom: ''
            };
        }

        // 保存到对应平台
        this.config.llm.apiKeys[currentPlatform] = apiKey;
    }

    // 获取所有平台的API Key
    getCurrentApiKeys() {
        const currentPlatform = document.getElementById('llm-platform').value;
        const currentApiKey = document.getElementById('llm-api-key').value;

        // 获取现有的API Keys
        const apiKeys = {
            openai: this.config.llm?.apiKeys?.openai || '',
            anthropic: this.config.llm?.apiKeys?.anthropic || '',
            google: this.config.llm?.apiKeys?.google || '',
            custom: this.config.llm?.apiKeys?.custom || ''
        };

        // 更新当前平台的API Key
        apiKeys[currentPlatform] = currentApiKey;

        return apiKeys;
    }

    // 更新UI状态显示
    updateUIStatus() {
        // 更新当前服务状态
        const currentService = this.config.ocrService || 'baidu';
        this.uiManager.updateCurrentService(currentService);

        // 更新识别状态
        this.uiManager.updateRecognitionStatus('ready', '就绪');
    }
}

// 初始化插件
document.addEventListener('DOMContentLoaded', () => {
    // 确保所有模块都已加载
    if (typeof ConfigManager !== 'undefined' &&
        typeof OCRServices !== 'undefined' &&
        typeof UIManager !== 'undefined' &&
        typeof ModelManager !== 'undefined') {
        window.ocrPlugin = new OCRPlugin();
        window.configManager = window.ocrPlugin.configManager;
        window.modelManager = window.ocrPlugin.modelManager;
    } else {
        console.error('OCR插件模块加载失败');
        console.log('已加载的模块:', {
            ConfigManager: typeof ConfigManager !== 'undefined',
            OCRServices: typeof OCRServices !== 'undefined',
            UIManager: typeof UIManager !== 'undefined',
            ModelManager: typeof ModelManager !== 'undefined'
        });
    }
});
